
import React from 'react';
import { Mail, Phone, MapPin, Clock, ArrowRight } from 'lucide-react';
import ContactForm from '../components/ContactForm';

const Contact = () => {
  return (
    <div className="page-transition pt-24">
      {/* Header */}
      <div className="bg-gradient-to-r from-primary/10 to-primary/5 py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="max-w-3xl">
            <span className="inline-block text-sm font-medium text-primary mb-2">Get in Touch</span>
            <h1 className="text-4xl md:text-5xl font-bold mb-6">Contact Us</h1>
            <p className="text-lg text-muted-foreground">
              We're here to help! Whether you have questions about our programs, admissions, or anything else, 
              our team is ready to assist you.
            </p>
          </div>
        </div>
      </div>
      
      {/* Contact Information */}
      <section className="py-12 bg-background">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {[
              {
                icon: <MapPin className="h-8 w-8 text-primary" />,
                title: 'Visit Us',
                details: [
                  '123 Education Street',
                  'Academic City, 10001',
                  'United States',
                ],
              },
              {
                icon: <Mail className="h-8 w-8 text-primary" />,
                title: 'Email Us',
                details: [
                  '<EMAIL>',
                  '<EMAIL>',
                  '<EMAIL>',
                ],
              },
              {
                icon: <Phone className="h-8 w-8 text-primary" />,
                title: 'Call Us',
                details: [
                  '(*************',
                  '(*************',
                  'Fax: (*************',
                ],
              },
            ].map((item, index) => (
              <div 
                key={index} 
                className="flex flex-col items-center text-center p-8 rounded-xl bg-card border border-border hover:shadow-md transition-all"
              >
                <div className="w-16 h-16 rounded-full bg-primary/10 flex items-center justify-center mb-4">
                  {item.icon}
                </div>
                <h3 className="text-xl font-bold mb-4">{item.title}</h3>
                <ul className="space-y-2">
                  {item.details.map((detail, i) => (
                    <li key={i} className="text-muted-foreground">{detail}</li>
                  ))}
                </ul>
              </div>
            ))}
          </div>
        </div>
      </section>
      
      {/* Contact Form and Map */}
      <section className="py-12 bg-background">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
            {/* Contact Form */}
            <div className="bg-card border border-border rounded-xl p-8 shadow-sm">
              <h2 className="text-2xl font-bold mb-6">Send Us a Message</h2>
              <ContactForm />
            </div>
            
            {/* Map and Hours */}
            <div className="space-y-8">
              {/* Map */}
              <div className="bg-card border border-border rounded-xl overflow-hidden shadow-sm">
                <div className="h-[300px] bg-gray-200 relative">
                  <iframe 
                    src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3022.2155261127076!2d-74.00714868459295!3d40.71329437933261!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x89c25a1648fa1463%3A0x7b0f7e89e9ba18eb!2sNew%20York%2C%20NY%2C%20USA!5e0!3m2!1sen!2sca!4v1650901892383!5m2!1sen!2sca" 
                    width="100%" 
                    height="100%" 
                    style={{ border: 0 }}
                    allowFullScreen={true}
                    loading="lazy" 
                    referrerPolicy="no-referrer-when-downgrade"
                    title="Academ Location"
                  ></iframe>
                </div>
                <div className="p-6">
                  <h3 className="text-xl font-bold mb-2">Our Location</h3>
                  <p className="text-muted-foreground">
                    Conveniently located in the heart of Academic City, our campus is easily accessible by public transportation.
                  </p>
                </div>
              </div>
              
              {/* Hours */}
              <div className="bg-card border border-border rounded-xl p-8 shadow-sm">
                <div className="flex items-start">
                  <div className="w-12 h-12 rounded-full bg-primary/10 flex items-center justify-center mr-4">
                    <Clock className="h-6 w-6 text-primary" />
                  </div>
                  <div>
                    <h3 className="text-xl font-bold mb-4">Office Hours</h3>
                    <ul className="space-y-3">
                      <li className="flex justify-between">
                        <span className="text-muted-foreground">Monday - Friday</span>
                        <span className="font-medium">8:00 AM - 6:00 PM</span>
                      </li>
                      <li className="flex justify-between">
                        <span className="text-muted-foreground">Saturday</span>
                        <span className="font-medium">9:00 AM - 1:00 PM</span>
                      </li>
                      <li className="flex justify-between">
                        <span className="text-muted-foreground">Sunday</span>
                        <span className="font-medium">Closed</span>
                      </li>
                    </ul>
                    <div className="mt-6 pt-6 border-t border-border">
                      <h4 className="font-semibold mb-2">Admissions Office</h4>
                      <p className="text-muted-foreground mb-4">
                        For admissions inquiries, our specialized team is available:
                      </p>
                      <p className="font-medium">Monday - Friday, 9:00 AM - 5:00 PM</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
      
      {/* FAQs */}
      <section className="py-20 bg-secondary">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center max-w-3xl mx-auto mb-16">
            <span className="inline-block text-sm font-medium text-primary mb-2">FAQ</span>
            <h2 className="text-3xl md:text-4xl font-bold mb-4">Frequently Asked Questions</h2>
            <p className="text-muted-foreground">
              Find answers to common questions about our institution and programs.
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8 max-w-4xl mx-auto">
            {[
              {
                question: 'What are the admission requirements?',
                answer: 'Admission requirements vary by program. Generally, we require an application form, academic transcripts, letters of recommendation, and in some cases, standardized test scores. Visit our Admissions page for program-specific requirements.',
              },
              {
                question: 'Do you offer financial aid or scholarships?',
                answer: 'Yes, we offer various financial aid options and merit-based scholarships to eligible students. Our Financial Aid office can help you explore options that may be available to you.',
              },
              {
                question: 'Can I transfer credits from another institution?',
                answer: 'We accept transfer credits from accredited institutions, subject to evaluation. Please contact our Admissions office with your specific transfer credit questions.',
              },
              {
                question: 'Do you offer online or part-time programs?',
                answer: 'Yes, we offer flexible learning options including online and part-time programs for many of our courses. Check specific program details for available formats.',
              },
              {
                question: 'What career services do you provide?',
                answer: 'Our Career Center offers comprehensive services including resume reviews, interview preparation, job search assistance, career counseling, and networking opportunities with industry partners.',
              },
              {
                question: 'How can I schedule a campus tour?',
                answer: 'You can schedule a campus tour through our website or by contacting our Admissions office. We offer both in-person and virtual tour options.',
              },
            ].map((faq, index) => (
              <div 
                key={index} 
                className="bg-card border border-border rounded-xl p-6 hover:shadow-md transition-all"
              >
                <h3 className="text-lg font-bold mb-3">{faq.question}</h3>
                <p className="text-muted-foreground">{faq.answer}</p>
              </div>
            ))}
          </div>
        </div>
      </section>
      
      {/* CTA */}
      <section className="py-20 bg-background">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="rounded-2xl overflow-hidden bg-gradient-to-r from-primary to-primary/80 shadow-xl">
            <div className="px-8 py-12 sm:px-12 sm:py-16 text-center text-white">
              <h2 className="text-3xl md:text-4xl font-bold mb-4">Ready to Begin Your Journey?</h2>
              <p className="text-white/90 text-lg max-w-3xl mx-auto mb-8">
                Take the first step towards a transformative educational experience at Academ.
              </p>
              <div className="flex flex-wrap justify-center gap-4">
                <a 
                  href="#" 
                  className="inline-flex items-center justify-center px-8 py-3 border border-transparent text-base font-medium rounded-md text-primary bg-white hover:bg-gray-50 transition-colors"
                >
                  Apply Now <ArrowRight className="ml-2 h-4 w-4" />
                </a>
                <a 
                  href="#" 
                  className="inline-flex items-center justify-center px-8 py-3 border border-white text-base font-medium rounded-md text-white hover:bg-white/10 transition-colors"
                >
                  Request Information
                </a>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
};

export default Contact;
