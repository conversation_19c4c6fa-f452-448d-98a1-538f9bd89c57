
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 220 14% 12%;

    --card: 0 0% 100%;
    --card-foreground: 220 14% 12%;

    --popover: 0 0% 100%;
    --popover-foreground: 220 14% 12%;

    --primary: 221 83% 53%;
    --primary-foreground: 210 40% 98%;

    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;

    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 221 83% 53%;

    --radius: 0.5rem;
  }

  .dark {
    --background: 220 14% 12%;
    --foreground: 210 40% 98%;

    --card: 220 14% 16%;
    --card-foreground: 210 40% 98%;

    --popover: 220 14% 16%;
    --popover-foreground: 210 40% 98%;

    --primary: 221 83% 53%;
    --primary-foreground: 210 40% 98%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 224.3 76.3% 48%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground font-sans;
  }

  h1, h2, h3, h4, h5, h6 {
    @apply font-display tracking-tight;
  }
}

@layer components {
  .nav-link {
    @apply relative px-2 py-1 text-foreground/80 font-medium transition-colors hover:text-foreground;
  }
  
  .nav-link::after {
    @apply content-[''] absolute bottom-0 left-0 w-full scale-x-0 h-0.5 bg-primary origin-bottom-right transition-transform duration-300;
  }
  
  .nav-link:hover::after {
    @apply scale-x-100 origin-bottom-left;
  }
  
  .nav-link.active {
    @apply text-foreground;
  }
  
  .nav-link.active::after {
    @apply scale-x-100;
  }

  .page-transition {
    @apply animate-fade-in;
  }

  .btn-primary {
    @apply relative inline-flex items-center justify-center overflow-hidden rounded-md bg-primary px-8 py-3 font-medium text-primary-foreground transition-all hover:bg-primary/90 focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2;
  }

  .btn-secondary {
    @apply relative inline-flex items-center justify-center overflow-hidden rounded-md border border-border bg-background px-8 py-3 font-medium text-foreground transition-all hover:bg-muted focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2;
  }

  .card-hover {
    @apply transition-all duration-300 hover:shadow-lg hover:-translate-y-1;
  }

  .glass-card {
    @apply bg-white/80 backdrop-blur-md shadow-sm border border-white/20;
  }
}
