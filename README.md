# Campus Folio 📚

A modern, responsive web application built with React and TypeScript, featuring a beautiful UI powered by shadcn/ui and Tailwind CSS.

🌐 **[Live Demo](https://sample-institute-page.netlify.app/)**


## 🚀 Features

- Modern React with TypeScript
- Beautiful UI components from shadcn/ui
- Responsive design with Tailwind CSS
- Fast development with Vite
- Form handling with React Hook Form and Zod validation
- Advanced UI components including:
  - Modals and dialogs
  - Toast notifications
  - Dropdowns and menus
  - Form elements
  - And much more!

## 🛠️ Tech Stack

- **Framework:** React 18
- **Language:** TypeScript
- **Build Tool:** Vite
- **UI Components:** shadcn/ui
- **Styling:** Tailwind CSS
- **Form Handling:** React Hook Form
- **Validation:** Zod
- **Routing:** React Router DOM
- **Data Fetching:** TanStack Query
- **Date Handling:** date-fns
- **Icons:** Lucide React

## 📦 Installation

1. Clone the repository:
```sh
git clone https://github.com/midlaj-muhammed/campus-folio.git
cd campus-folio
```

2. Install dependencies:
```sh
npm install
```

3. Start the development server:
```sh
npm run dev
```

The application will be available at `http://localhost:5173`

## 🔧 Available Scripts

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run build:dev` - Build for development
- `npm run preview` - Preview production build
- `npm run lint` - Run ESLint

## 📁 Project Structure

```
campus-folio/
├── src/              # Source files
├── public/           # Static files
├── components.json   # shadcn/ui configuration
├── tailwind.config.ts # Tailwind CSS configuration
├── vite.config.ts    # Vite configuration
└── tsconfig.json     # TypeScript configuration
```

## 🔨 Development

1. Make sure you have Node.js installed (LTS version recommended)
2. Install dependencies using `npm install`
3. Start the development server using `npm run dev`
4. Make your changes
5. Run `npm run lint` to check for any code style issues
6. Build the project using `npm run build` to ensure everything compiles correctly

## 🚀 Deployment

To build the application for production:

```sh
npm run build
```

The built files will be in the `dist` directory, ready to be deployed to your hosting platform of choice.

## 📝 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🤝 Contributing

Contributions, issues, and feature requests are welcome! Feel free to check the issues page.

1. Fork the project
2. Create your feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add some amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request
