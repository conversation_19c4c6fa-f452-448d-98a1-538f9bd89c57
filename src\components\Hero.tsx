

import { Link } from 'react-router-dom';
import { ArrowRight } from 'lucide-react';

const Hero = () => {
  return (
    <div className="relative overflow-hidden bg-background pt-24 pb-16 sm:pt-32">
      <div className="absolute inset-0 z-0">
        <div className="absolute inset-0 bg-gradient-to-r from-primary/5 to-secondary/20" />
      </div>

      <div className="relative mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 py-24 grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
        <div className="max-w-2xl animate-slide-in">
          <div className="inline-flex items-center px-3 py-1 mb-4 rounded-full bg-primary/10 text-primary text-sm font-medium">
            <span>Excellence in education</span>
          </div>
          
          <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold text-foreground mb-6 tracking-tight leading-tight">
            Empowering Minds, <br />
            <span className="text-primary">Shaping Futures</span>
          </h1>
          
          <p className="text-lg text-muted-foreground mb-8 max-w-lg">
            Discover a world-class education experience that prepares you for success
            in today's rapidly evolving global landscape.
          </p>
          
          <div className="flex flex-wrap gap-4">
            <Link to="/courses" className="btn-primary">
              Explore Courses
            </Link>
            <Link to="/about" className="btn-secondary flex items-center">
              Learn More <ArrowRight className="ml-2 h-4 w-4" />
            </Link>
          </div>
          
          <div className="mt-10 flex items-center gap-6">
            <div className="flex -space-x-2">
              {[1, 2, 3, 4].map((i) => (
                <div key={i} className="inline-block h-10 w-10 rounded-full ring-2 ring-background overflow-hidden">
                  <img 
                    src={`https://randomuser.me/api/portraits/${i % 2 === 0 ? 'women' : 'men'}/${i + 50}.jpg`} 
                    alt={`User ${i}`}
                    className="h-full w-full object-cover"
                  />
                </div>
              ))}
            </div>
            <div>
              <p className="text-sm font-semibold text-foreground">Join 2,000+ students</p>
              <div className="flex text-amber-400 text-sm mt-1">
                {'★'.repeat(5)} <span className="text-muted-foreground ml-1 text-xs">(4.9/5)</span>
              </div>
            </div>
          </div>
        </div>
        
        <div className="relative h-[400px] sm:h-[500px] lg:h-[600px] animate-fade-in block">
          <div className="absolute top-0 right-0 w-full h-full rounded-2xl overflow-hidden">
            <img
              src="https://images.unsplash.com/photo-1541339907198-e08756dedf3f?ixlib=rb-4.0.3&auto=format&fit=crop&w=1170&q=80"
              alt="Students in graduation ceremony"
              className="w-full h-full object-cover"
              onError={(e) => {
                console.error('Image failed to load:', e);
                e.currentTarget.src = 'https://via.placeholder.com/1170x600/3b82f6/ffffff?text=Campus+Image';
              }}
            />
            <div className="absolute inset-0 bg-gradient-to-t from-background/40 to-transparent"></div>
          </div>
          
          <div className="absolute -bottom-6 -left-12 w-72 h-32 glass-card rounded-lg p-4 animate-float">
            <div className="flex items-center space-x-4">
              <div className="h-12 w-12 rounded-full bg-primary/20 flex items-center justify-center">
                <span className="text-primary text-lg font-bold">98%</span>
              </div>
              <div>
                <h3 className="text-sm font-semibold">Graduate Success Rate</h3>
                <p className="text-xs text-muted-foreground">Career placement within 6 months</p>
              </div>
            </div>
          </div>
          
          <div className="absolute top-10 -right-8 w-64 h-28 glass-card rounded-lg p-4 animate-float">
            <div className="flex items-center space-x-4">
              <div className="h-12 w-12 rounded-full bg-green-500/20 flex items-center justify-center">
                <span className="text-green-500 text-lg font-bold">25+</span>
              </div>
              <div>
                <h3 className="text-sm font-semibold">Global Recognition</h3>
                <p className="text-xs text-muted-foreground">Top-ranked programs</p>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <div className="relative mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 mt-16 sm:mt-24">
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 md:gap-8">
          {['Harvard University', 'Stanford', 'MIT', 'Oxford'].map((partner, index) => (
            <div key={index} className="flex items-center justify-center py-5 px-8 grayscale opacity-70 hover:grayscale-0 hover:opacity-100 transition-all">
              <span className="text-xl font-bold text-muted-foreground">{partner}</span>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default Hero;
