
import React from 'react';
import { <PERSON> } from 'react-router-dom';
import { ArrowRight, Users, BookOpen, GraduationCap, Globe, ArrowUpRight } from 'lucide-react';
import Hero from '../components/Hero';
import CourseCard, { CourseCardProps } from '../components/CourseCard';
import FacultyCard, { FacultyCardProps } from '../components/FacultyCard';

const featuredCourses: CourseCardProps[] = [
  {
    id: 1,
    title: 'Introduction to Computer Science',
    category: 'Computer Science',
    image: 'https://images.unsplash.com/photo-1587620962725-abab7fe55159?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1170&q=80',
    instructor: 'Dr. <PERSON>',
    duration: '8 weeks',
    level: 'Beginner',
    students: 1543,
    price: 0,
    featured: true,
  },
  {
    id: 2,
    title: 'Advanced Business Analytics',
    category: 'Business',
    image: 'https://images.unsplash.com/photo-1460925895917-afdab827c52f?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1115&q=80',
    instructor: 'Prof. Michael Chen',
    duration: '12 weeks',
    level: 'Advanced',
    students: 876,
    price: 89.99,
  },
  {
    id: 3,
    title: 'Modern Web Development',
    category: 'Web Development',
    image: 'https://images.unsplash.com/photo-1498050108023-c5249f4df085?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1172&q=80',
    instructor: 'Emily Rodriguez',
    duration: '10 weeks',
    level: 'Intermediate',
    students: 1232,
    price: 69.99,
  },
];

const featuredFaculty: FacultyCardProps[] = [
  {
    id: 1,
    name: 'Dr. Sarah Johnson',
    position: 'Professor of Computer Science',
    image: 'https://images.unsplash.com/photo-1573496359142-b8d87734a5a2?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=688&q=80',
    bio: 'Dr. Johnson has over 15 years of experience in computer science research and education. She specializes in artificial intelligence and machine learning.',
    socialLinks: {
      linkedin: '#',
      twitter: '#',
    },
  },
  {
    id: 2,
    name: 'Prof. Michael Chen',
    position: 'Chair of Business Department',
    image: 'https://images.unsplash.com/photo-1566492031773-4f4e44671857?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=687&q=80',
    bio: 'Professor Chen is an expert in business analytics and strategic management with extensive consulting experience for Fortune 500 companies.',
    socialLinks: {
      linkedin: '#',
      facebook: '#',
    },
  },
  {
    id: 3,
    name: 'Emily Rodriguez',
    position: 'Web Development Instructor',
    image: 'https://images.unsplash.com/photo-1580489944761-15a19d654956?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=761&q=80',
    bio: 'Emily is a seasoned web developer with a decade of industry experience. She has worked with major tech companies and now focuses on teaching the next generation of developers.',
    socialLinks: {
      twitter: '#',
      linkedin: '#',
    },
  },
  {
    id: 4,
    name: 'Dr. James Wilson',
    position: 'Professor of Physics',
    image: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1170&q=80',
    bio: 'Dr. Wilson is a theoretical physicist with research interests in quantum mechanics and astrophysics. He has published numerous papers in prestigious scientific journals.',
    socialLinks: {
      linkedin: '#',
    },
  },
];

const Index = () => {
  return (
    <div className="page-transition">
      <Hero />
      
      {/* Features Section */}
      <section className="py-20 bg-background">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center max-w-3xl mx-auto mb-16">
            <span className="inline-block text-sm font-medium text-primary mb-2">Why Choose Us</span>
            <h2 className="text-3xl md:text-4xl font-bold mb-4">Excellence in Education</h2>
            <p className="text-muted-foreground">
              Discover why thousands of students choose Academ for their educational journey.
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {[
              {
                icon: <Users className="h-10 w-10 text-primary" />,
                title: 'Expert Faculty',
                description: 'Learn from industry experts and accomplished academics.',
              },
              {
                icon: <BookOpen className="h-10 w-10 text-primary" />,
                title: 'Modern Curriculum',
                description: "Courses designed to meet the demands of today's industries.",
              },
              {
                icon: <GraduationCap className="h-10 w-10 text-primary" />,
                title: 'Career Support',
                description: 'Personalized career guidance and placement assistance.',
              },
              {
                icon: <Globe className="h-10 w-10 text-primary" />,
                title: 'Global Community',
                description: 'Join a diverse network of students from around the world.',
              },
            ].map((feature, index) => (
              <div 
                key={index} 
                className="p-8 rounded-lg bg-card border border-border hover:shadow-md transition-all animate-fade-in"
                style={{ animationDelay: `${index * 100}ms` }}
              >
                <div className="w-14 h-14 rounded-full bg-primary/10 flex items-center justify-center mb-6">
                  {feature.icon}
                </div>
                <h3 className="text-xl font-semibold mb-3">{feature.title}</h3>
                <p className="text-muted-foreground">{feature.description}</p>
              </div>
            ))}
          </div>
        </div>
      </section>
      
      {/* Featured Courses Section */}
      <section className="py-20 bg-muted/50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex flex-col md:flex-row md:items-end md:justify-between mb-12">
            <div>
              <span className="inline-block text-sm font-medium text-primary mb-2">Featured Courses</span>
              <h2 className="text-3xl md:text-4xl font-bold">Popular Courses</h2>
            </div>
            <Link 
              to="/courses" 
              className="inline-flex items-center text-primary hover:text-primary-foreground hover:bg-primary/80 px-4 py-2 rounded-md transition-colors mt-4 md:mt-0"
            >
              View All Courses <ArrowRight className="ml-2 h-4 w-4" />
            </Link>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {featuredCourses.map((course) => (
              <CourseCard key={course.id} {...course} />
            ))}
          </div>
        </div>
      </section>
      
      {/* Stats Section */}
      <section className="py-20 bg-gradient-to-r from-primary/90 to-primary/70 text-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
            {[
              { number: '25+', label: 'Years of Excellence' },
              { number: '200+', label: 'Expert Instructors' },
              { number: '15,000+', label: 'Students Enrolled' },
              { number: '98%', label: 'Success Rate' },
            ].map((stat, index) => (
              <div key={index} className="text-center">
                <div className="text-4xl md:text-5xl font-bold mb-2">{stat.number}</div>
                <div className="text-white/80">{stat.label}</div>
              </div>
            ))}
          </div>
        </div>
      </section>
      
      {/* Faculty Section */}
      <section className="py-20 bg-background">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center max-w-3xl mx-auto mb-16">
            <span className="inline-block text-sm font-medium text-primary mb-2">Our Faculty</span>
            <h2 className="text-3xl md:text-4xl font-bold mb-4">Meet Our Expert Faculty</h2>
            <p className="text-muted-foreground">
              Our experienced faculty members are dedicated to helping you succeed in your educational journey.
            </p>
          </div>
          
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-8">
            {featuredFaculty.map((faculty) => (
              <FacultyCard key={faculty.id} {...faculty} />
            ))}
          </div>
        </div>
      </section>
      
      {/* Testimonial Section */}
      <section className="py-20 bg-secondary">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center max-w-3xl mx-auto mb-16">
            <span className="inline-block text-sm font-medium text-primary mb-2">Testimonials</span>
            <h2 className="text-3xl md:text-4xl font-bold mb-4">What Our Students Say</h2>
            <p className="text-muted-foreground">
              Hear from our students about their experience at Academ.
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {[
              {
                quote: "The quality of education and support I received at Academ was exceptional. The faculty truly cares about student success.",
                name: "Jessica Chen",
                role: "Computer Science Graduate, 2022",
                image: "https://randomuser.me/api/portraits/women/44.jpg",
              },
              {
                quote: "Enrolling at Academ was one of the best decisions I've made. The practical approach to learning prepared me well for the industry.",
                name: "Michael Rodriguez",
                role: "Business Analytics Graduate, 2021",
                image: "https://randomuser.me/api/portraits/men/32.jpg",
              },
              {
                quote: "The global network and industry connections I made through Academ have been invaluable for my career growth.",
                name: "Sarah Johnson",
                role: "Web Development Graduate, 2023",
                image: "https://randomuser.me/api/portraits/women/68.jpg",
              },
            ].map((testimonial, index) => (
              <div 
                key={index} 
                className="bg-card border border-border p-8 rounded-lg shadow-sm"
              >
                <div className="flex flex-col h-full">
                  <div className="mb-6 flex-grow">
                    <svg className="h-8 w-8 text-primary/20 mb-4" fill="currentColor" viewBox="0 0 32 32" aria-hidden="true">
                      <path d="M9.352 4C4.456 7.456 1 13.12 1 19.36c0 5.088 3.072 8.064 6.624 8.064 3.36 0 5.856-2.688 5.856-5.856 0-3.168-2.208-5.472-5.088-5.472-.576 0-1.344.096-1.536.192.48-3.264 3.552-7.104 6.624-9.024L9.352 4zm16.512 0c-4.8 3.456-8.256 9.12-8.256 15.36 0 5.088 3.072 8.064 6.624 8.064 3.264 0 5.856-2.688 5.856-5.856 0-3.168-2.304-5.472-5.184-5.472-.576 0-1.248.096-1.44.192.48-3.264 3.456-7.104 6.528-9.024L25.864 4z" />
                    </svg>
                    <p className="text-foreground italic">{testimonial.quote}</p>
                  </div>
                  <div className="flex items-center mt-4">
                    <img 
                      src={testimonial.image} 
                      alt={testimonial.name}
                      className="h-10 w-10 rounded-full mr-3"
                    />
                    <div>
                      <h4 className="text-sm font-semibold">{testimonial.name}</h4>
                      <p className="text-xs text-muted-foreground">{testimonial.role}</p>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>
      
      {/* CTA Section */}
      <section className="py-20 bg-gradient-to-r from-primary/5 to-primary/10">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="rounded-2xl overflow-hidden bg-card border border-border shadow-lg p-8 md:p-12">
            <div className="grid md:grid-cols-2 gap-8 items-center">
              <div>
                <span className="inline-block text-sm font-medium text-primary mb-2">Start Your Journey</span>
                <h2 className="text-3xl md:text-4xl font-bold mb-4">Ready to Transform Your Future?</h2>
                <p className="text-muted-foreground mb-6">
                  Take the first step towards a brighter future. Apply now to join our community of learners and achieve your educational goals.
                </p>
                <div className="flex flex-wrap gap-4">
                  <Link to="/courses" className="btn-primary">
                    Explore Courses
                  </Link>
                  <Link to="/contact" className="btn-secondary flex items-center">
                    Contact Us <ArrowUpRight className="ml-2 h-4 w-4" />
                  </Link>
                </div>
              </div>
              <div className="relative h-64 md:h-full min-h-[300px] rounded-xl overflow-hidden">
                <img 
                  src="https://images.unsplash.com/photo-1522202176988-66273c2fd55f?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1171&q=80" 
                  alt="Students collaborating" 
                  className="absolute inset-0 w-full h-full object-cover"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/40 to-transparent"></div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
};

export default Index;
