
import React from 'react';
import { Facebook, Twitter, Linkedin } from 'lucide-react';

export interface FacultyCardProps {
  id: number;
  name: string;
  position: string;
  image: string;
  bio: string;
  socialLinks?: {
    facebook?: string;
    twitter?: string;
    linkedin?: string;
  };
}

const FacultyCard: React.FC<FacultyCardProps> = ({
  id,
  name,
  position,
  image,
  bio,
  socialLinks = {},
}) => {
  return (
    <div className="group rounded-xl overflow-hidden bg-card shadow-sm border border-border card-hover">
      <div className="relative">
        <img 
          src={image} 
          alt={name}
          className="w-full h-64 object-cover transition-transform duration-500 group-hover:scale-105"
        />
        <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent opacity-0 group-hover:opacity-100 transition-opacity">
          <div className="absolute bottom-0 left-0 right-0 p-4">
            <div className="flex justify-center space-x-3">
              {socialLinks.facebook && (
                <a 
                  href={socialLinks.facebook} 
                  target="_blank" 
                  rel="noopener noreferrer"
                  className="w-8 h-8 rounded-full bg-white/20 backdrop-blur-sm flex items-center justify-center text-white hover:bg-primary hover:text-white transition-colors"
                >
                  <Facebook className="h-4 w-4" />
                </a>
              )}
              {socialLinks.twitter && (
                <a 
                  href={socialLinks.twitter} 
                  target="_blank" 
                  rel="noopener noreferrer"
                  className="w-8 h-8 rounded-full bg-white/20 backdrop-blur-sm flex items-center justify-center text-white hover:bg-primary hover:text-white transition-colors"
                >
                  <Twitter className="h-4 w-4" />
                </a>
              )}
              {socialLinks.linkedin && (
                <a 
                  href={socialLinks.linkedin} 
                  target="_blank" 
                  rel="noopener noreferrer"
                  className="w-8 h-8 rounded-full bg-white/20 backdrop-blur-sm flex items-center justify-center text-white hover:bg-primary hover:text-white transition-colors"
                >
                  <Linkedin className="h-4 w-4" />
                </a>
              )}
            </div>
          </div>
        </div>
      </div>
      
      <div className="p-5">
        <h3 className="text-lg font-semibold mb-1">{name}</h3>
        <p className="text-sm text-primary mb-3">{position}</p>
        <p className="text-sm text-muted-foreground line-clamp-3">{bio}</p>
      </div>
    </div>
  );
};

export default FacultyCard;
