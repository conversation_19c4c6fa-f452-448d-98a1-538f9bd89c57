
import React from 'react';
import { <PERSON><PERSON><PERSON>, Award, Book, GraduationCap, Map, Users } from 'lucide-react';
import { <PERSON> } from 'react-router-dom';
import FacultyCard, { FacultyCardProps } from '../components/FacultyCard';

const facultyMembers: FacultyCardProps[] = [
  {
    id: 1,
    name: 'Dr. <PERSON>',
    position: 'Professor of Computer Science',
    image: 'https://images.unsplash.com/photo-1573496359142-b8d87734a5a2?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=688&q=80',
    bio: 'Dr. <PERSON> has over 15 years of experience in computer science research and education. She specializes in artificial intelligence and machine learning.',
    socialLinks: {
      linkedin: '#',
      twitter: '#',
    },
  },
  {
    id: 2,
    name: 'Prof. <PERSON>',
    position: 'Chair of Business Department',
    image: 'https://images.unsplash.com/photo-1566492031773-4f4e44671857?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=687&q=80',
    bio: 'Professor Chen is an expert in business analytics and strategic management with extensive consulting experience for Fortune 500 companies.',
    socialLinks: {
      linkedin: '#',
      facebook: '#',
    },
  },
  {
    id: 3,
    name: 'Emily Rodriguez',
    position: 'Web Development Instructor',
    image: 'https://images.unsplash.com/photo-1580489944761-15a19d654956?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=761&q=80',
    bio: 'Emily is a seasoned web developer with a decade of industry experience. She has worked with major tech companies and now focuses on teaching the next generation of developers.',
    socialLinks: {
      twitter: '#',
      linkedin: '#',
    },
  },
  {
    id: 4,
    name: 'Dr. James Wilson',
    position: 'Professor of Physics',
    image: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1170&q=80',
    bio: 'Dr. Wilson is a theoretical physicist with research interests in quantum mechanics and astrophysics. He has published numerous papers in prestigious scientific journals.',
    socialLinks: {
      linkedin: '#',
    },
  },
  {
    id: 5,
    name: 'Prof. Jennifer Lee',
    position: 'Economics Department Head',
    image: 'https://images.unsplash.com/photo-1551836022-d5d88e9218df?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=687&q=80',
    bio: 'Professor Lee is a renowned economist with expertise in international trade and development economics. She has advised several international organizations.',
    socialLinks: {
      twitter: '#',
      linkedin: '#',
    },
  },
  {
    id: 6,
    name: 'Dr. David Thompson',
    position: 'Psychology Professor',
    image: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=687&q=80',
    bio: 'Dr. Thompson has dedicated his career to understanding human behavior. His research focuses on cognitive psychology and its applications in education.',
    socialLinks: {
      facebook: '#',
      linkedin: '#',
    },
  },
  {
    id: 7,
    name: 'Alexandra Rivera',
    position: 'Graphic Design Instructor',
    image: 'https://images.unsplash.com/photo-1494790108377-be9c29b29330?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=687&q=80',
    bio: 'Alexandra brings her industry experience as a creative director to the classroom. Her teaching emphasizes practical skills and creative problem-solving.',
    socialLinks: {
      twitter: '#',
      linkedin: '#',
    },
  },
  {
    id: 8,
    name: 'Dr. Robert Kim',
    position: 'Data Science Professor',
    image: 'https://images.unsplash.com/photo-1500648767791-00dcc994a43e?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=687&q=80',
    bio: 'Dr. Kim combines theoretical knowledge with practical applications in data science. His research focuses on big data analytics and machine learning algorithms.',
    socialLinks: {
      linkedin: '#',
    },
  },
];

const About = () => {
  return (
    <div className="page-transition pt-24">
      {/* Hero Section */}
      <div className="bg-gradient-to-r from-primary/10 to-primary/5 py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="md:flex md:items-center md:justify-between">
            <div className="md:w-1/2 mb-12 md:mb-0 pr-0 md:pr-12">
              <span className="inline-block text-sm font-medium text-primary mb-2">About Us</span>
              <h1 className="text-4xl md:text-5xl font-bold mb-6">Our Journey of Excellence</h1>
              <p className="text-lg text-muted-foreground mb-8">
                For over 25 years, Academ has been at the forefront of educational innovation, 
                providing exceptional learning experiences that prepare students for success in a rapidly evolving world.
              </p>
              <div className="flex flex-wrap gap-4">
                <Link to="/courses" className="btn-primary">
                  Our Courses
                </Link>
                <Link to="/contact" className="btn-secondary flex items-center">
                  Get in Touch <ArrowRight className="ml-2 h-4 w-4" />
                </Link>
              </div>
            </div>
            <div className="md:w-1/2 relative">
              <div className="aspect-w-16 aspect-h-9 rounded-xl overflow-hidden shadow-xl">
                <img 
                  src="https://images.unsplash.com/photo-1523050854058-8df90110c9f1?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1170&q=80" 
                  alt="University campus"
                  className="w-full h-full object-cover"
                />
              </div>
              <div className="absolute -bottom-6 -left-6 glass-card rounded-lg p-4 shadow-lg max-w-xs animate-float">
                <div className="flex items-center space-x-3">
                  <div className="h-12 w-12 rounded-full bg-primary/20 flex items-center justify-center">
                    <GraduationCap className="h-6 w-6 text-primary" />
                  </div>
                  <div>
                    <h3 className="text-sm font-semibold">Founded in 1995</h3>
                    <p className="text-xs text-muted-foreground">25+ years of educational excellence</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      {/* Mission and Vision */}
      <section className="py-20 bg-background">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
            <div className="rounded-xl overflow-hidden bg-card shadow-sm border border-border p-8">
              <div className="w-14 h-14 rounded-full bg-primary/10 flex items-center justify-center mb-6">
                <Book className="h-6 w-6 text-primary" />
              </div>
              <h2 className="text-2xl font-bold mb-4">Our Mission</h2>
              <p className="text-muted-foreground mb-6">
                At Academ, our mission is to empower individuals through transformative education that fosters intellectual growth, 
                critical thinking, and practical skills. We are committed to providing an inclusive learning environment 
                where students from diverse backgrounds can thrive and prepare for meaningful careers.
              </p>
              <ul className="space-y-3">
                {['Excellence in teaching and learning', 'Innovative research and scholarship', 'Community engagement and service'].map((item, index) => (
                  <li key={index} className="flex items-start">
                    <div className="flex-shrink-0 h-6 w-6 rounded-full bg-green-100 flex items-center justify-center mr-3">
                      <span className="text-green-500 text-sm">✓</span>
                    </div>
                    <span className="text-foreground">{item}</span>
                  </li>
                ))}
              </ul>
            </div>
            
            <div className="rounded-xl overflow-hidden bg-card shadow-sm border border-border p-8">
              <div className="w-14 h-14 rounded-full bg-primary/10 flex items-center justify-center mb-6">
                <Award className="h-6 w-6 text-primary" />
              </div>
              <h2 className="text-2xl font-bold mb-4">Our Vision</h2>
              <p className="text-muted-foreground mb-6">
                We envision Academ as a global leader in education, recognized for academic excellence, 
                innovative teaching methodologies, and impactful research. We aspire to nurture the next generation 
                of leaders who will address complex global challenges and make meaningful contributions to society.
              </p>
              <ul className="space-y-3">
                {['Global recognition for academic excellence', 'Pioneering educational innovation', 'Preparing ethical, forward-thinking leaders'].map((item, index) => (
                  <li key={index} className="flex items-start">
                    <div className="flex-shrink-0 h-6 w-6 rounded-full bg-blue-100 flex items-center justify-center mr-3">
                      <span className="text-blue-500 text-sm">★</span>
                    </div>
                    <span className="text-foreground">{item}</span>
                  </li>
                ))}
              </ul>
            </div>
          </div>
        </div>
      </section>
      
      {/* History Timeline */}
      <section className="py-20 bg-gradient-to-r from-primary/5 to-primary/10">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center max-w-3xl mx-auto mb-16">
            <span className="inline-block text-sm font-medium text-primary mb-2">Our History</span>
            <h2 className="text-3xl md:text-4xl font-bold mb-4">A Legacy of Impact</h2>
            <p className="text-muted-foreground">
              From our humble beginnings to our current position as a leading institution, 
              our history reflects our unwavering commitment to educational excellence.
            </p>
          </div>
          
          <div className="relative">
            {/* Vertical Line */}
            <div className="absolute left-1/2 transform -translate-x-1/2 h-full w-0.5 bg-border"></div>
            
            {/* Timeline Items */}
            <div className="space-y-12">
              {[
                {
                  year: '1995',
                  title: 'Foundation',
                  description: 'Academ was founded with the vision of providing accessible, high-quality education.',
                  image: 'https://images.unsplash.com/photo-1517245386807-bb43f82c33c4?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1170&q=80',
                },
                {
                  year: '2003',
                  title: 'Campus Expansion',
                  description: 'Major expansion of campus facilities to accommodate growing student enrollment.',
                  image: 'https://images.unsplash.com/photo-1498243691581-b145c3f54a5a?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1170&q=80',
                },
                {
                  year: '2010',
                  title: 'International Recognition',
                  description: 'Academ received international accreditation and recognition for academic excellence.',
                  image: 'https://images.unsplash.com/photo-1532649538693-f3a2ec1bf8bd?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1170&q=80',
                },
                {
                  year: '2018',
                  title: 'Digital Transformation',
                  description: 'Launch of comprehensive online learning platforms and digital resources.',
                  image: 'https://images.unsplash.com/photo-1581092580497-e0d23cbdf1dc?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1170&q=80',
                },
                {
                  year: 'Today',
                  title: 'Global Impact',
                  description: 'Continuing our mission to provide exceptional education to students worldwide.',
                  image: 'https://images.unsplash.com/photo-1523240795612-9a054b0db644?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1170&q=80',
                },
              ].map((item, index) => (
                <div key={index} className="relative">
                  <div className={`flex items-center ${index % 2 === 0 ? 'flex-row-reverse' : ''}`}>
                    {/* Content */}
                    <div className={`w-1/2 ${index % 2 === 0 ? 'pr-12 text-right' : 'pl-12'}`}>
                      <div className="inline-block bg-primary text-white text-sm font-bold px-3 py-1 rounded mb-2">
                        {item.year}
                      </div>
                      <h3 className="text-xl font-bold mb-2">{item.title}</h3>
                      <p className="text-muted-foreground">{item.description}</p>
                    </div>
                    
                    {/* Circle */}
                    <div className="absolute left-1/2 transform -translate-x-1/2 w-8 h-8 rounded-full bg-primary border-4 border-background"></div>
                    
                    {/* Image */}
                    <div className={`w-1/2 ${index % 2 === 0 ? 'pl-12' : 'pr-12'}`}>
                      <div className="rounded-lg overflow-hidden shadow-md">
                        <img 
                          src={item.image} 
                          alt={item.title} 
                          className="w-full h-48 object-cover"
                        />
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>
      
      {/* Our Values */}
      <section className="py-20 bg-background">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center max-w-3xl mx-auto mb-16">
            <span className="inline-block text-sm font-medium text-primary mb-2">Our Values</span>
            <h2 className="text-3xl md:text-4xl font-bold mb-4">Principles That Guide Us</h2>
            <p className="text-muted-foreground">
              Our core values shape everything we do, from curriculum development to campus culture.
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {[
              {
                icon: <Users className="h-8 w-8 text-primary" />,
                title: 'Inclusivity',
                description: 'We embrace diversity and create a welcoming environment for all students, regardless of background or circumstance.',
              },
              {
                icon: <Award className="h-8 w-8 text-primary" />,
                title: 'Excellence',
                description: 'We pursue the highest standards in teaching, research, and institutional practices.',
              },
              {
                icon: <Map className="h-8 w-8 text-primary" />,
                title: 'Innovation',
                description: 'We encourage creative thinking and the exploration of new ideas and approaches.',
              },
              {
                icon: <GraduationCap className="h-8 w-8 text-primary" />,
                title: 'Student-Centered',
                description: 'We prioritize student success and well-being in all our decisions and actions.',
              },
              {
                icon: <Book className="h-8 w-8 text-primary" />,
                title: 'Integrity',
                description: 'We operate with honesty, transparency, and ethical principles in all our endeavors.',
              },
              {
                icon: <Users className="h-8 w-8 text-primary" />,
                title: 'Community',
                description: 'We foster meaningful connections and collaborative relationships within and beyond our campus.',
              },
            ].map((value, index) => (
              <div 
                key={index} 
                className="bg-card border border-border rounded-xl p-8 hover:shadow-md transition-all"
              >
                <div className="w-14 h-14 rounded-full bg-primary/10 flex items-center justify-center mb-6">
                  {value.icon}
                </div>
                <h3 className="text-xl font-bold mb-3">{value.title}</h3>
                <p className="text-muted-foreground">{value.description}</p>
              </div>
            ))}
          </div>
        </div>
      </section>
      
      {/* Faculty */}
      <section className="py-20 bg-secondary">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center max-w-3xl mx-auto mb-16">
            <span className="inline-block text-sm font-medium text-primary mb-2">Our Faculty</span>
            <h2 className="text-3xl md:text-4xl font-bold mb-4">Meet Our Expert Faculty</h2>
            <p className="text-muted-foreground">
              Our accomplished faculty members bring a wealth of knowledge, experience, and dedication to the classroom.
            </p>
          </div>
          
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-8">
            {facultyMembers.map((faculty) => (
              <FacultyCard key={faculty.id} {...faculty} />
            ))}
          </div>
        </div>
      </section>
      
      {/* CTA Section */}
      <section className="py-20 bg-background">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="rounded-2xl overflow-hidden bg-gradient-to-r from-primary to-primary/80 shadow-xl">
            <div className="px-8 py-12 sm:px-12 sm:py-16 text-center text-white">
              <h2 className="text-3xl md:text-4xl font-bold mb-4">Join Our Learning Community</h2>
              <p className="text-white/90 text-lg max-w-3xl mx-auto mb-8">
                Take the first step towards a transformative educational experience. 
                Explore our programs and discover how Academ can help you achieve your goals.
              </p>
              <div className="flex flex-wrap justify-center gap-4">
                <Link 
                  to="/courses" 
                  className="inline-flex items-center justify-center px-8 py-3 border border-transparent text-base font-medium rounded-md text-primary bg-white hover:bg-gray-50 transition-colors"
                >
                  Explore Courses
                </Link>
                <Link 
                  to="/contact" 
                  className="inline-flex items-center justify-center px-8 py-3 border border-white text-base font-medium rounded-md text-white hover:bg-white/10 transition-colors"
                >
                  Contact Us
                </Link>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
};

export default About;
