
import React, { useState } from 'react';
import { Search, Filter, ChevronDown } from 'lucide-react';
import CourseCard, { CourseCardProps } from '../components/CourseCard';

const allCourses: CourseCardProps[] = [
  {
    id: 1,
    title: 'Introduction to Computer Science',
    category: 'Computer Science',
    image: 'https://images.unsplash.com/photo-1587620962725-abab7fe55159?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1170&q=80',
    instructor: 'Dr. <PERSON>',
    duration: '8 weeks',
    level: 'Beginner',
    students: 1543,
    price: 0,
    featured: true,
  },
  {
    id: 2,
    title: 'Advanced Business Analytics',
    category: 'Business',
    image: 'https://images.unsplash.com/photo-1460925895917-afdab827c52f?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1115&q=80',
    instructor: 'Prof. <PERSON>',
    duration: '12 weeks',
    level: 'Advanced',
    students: 876,
    price: 89.99,
  },
  {
    id: 3,
    title: 'Modern Web Development',
    category: 'Web Development',
    image: 'https://images.unsplash.com/photo-1498050108023-c5249f4df085?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1172&q=80',
    instructor: 'Emily Rodriguez',
    duration: '10 weeks',
    level: 'Intermediate',
    students: 1232,
    price: 69.99,
  },
  {
    id: 4,
    title: 'Data Science Fundamentals',
    category: 'Data Science',
    image: 'https://images.unsplash.com/photo-1551288049-bebda4e38f71?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1170&q=80',
    instructor: 'Dr. James Wilson',
    duration: '14 weeks',
    level: 'Intermediate',
    students: 954,
    price: 79.99,
  },
  {
    id: 5,
    title: 'Digital Marketing Essentials',
    category: 'Marketing',
    image: 'https://images.unsplash.com/photo-1533750349088-cd871a92f312?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1170&q=80',
    instructor: 'Lisa Thompson',
    duration: '8 weeks',
    level: 'Beginner',
    students: 1876,
    price: 59.99,
  },
  {
    id: 6,
    title: 'Artificial Intelligence and Machine Learning',
    category: 'Computer Science',
    image: 'https://images.unsplash.com/photo-1620712943543-bcc4688e7485?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=765&q=80',
    instructor: 'Dr. Robert Kim',
    duration: '16 weeks',
    level: 'Advanced',
    students: 698,
    price: 99.99,
  },
  {
    id: 7,
    title: 'Principles of Economics',
    category: 'Economics',
    image: 'https://images.unsplash.com/photo-1607265578041-35cb0d320ff4?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=627&q=80',
    instructor: 'Prof. Amanda Lewis',
    duration: '10 weeks',
    level: 'Beginner',
    students: 1432,
    price: 49.99,
  },
  {
    id: 8,
    title: 'Graphic Design Masterclass',
    category: 'Design',
    image: 'https://images.unsplash.com/photo-1626785774573-4b799315345d?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1171&q=80',
    instructor: 'Daniel Martinez',
    duration: '12 weeks',
    level: 'Intermediate',
    students: 2134,
    price: 69.99,
  },
  {
    id: 9,
    title: 'Introduction to Psychology',
    category: 'Psychology',
    image: 'https://images.unsplash.com/photo-1621243071264-23cf7d85b8df?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1170&q=80',
    instructor: 'Prof. Rachel Green',
    duration: '8 weeks',
    level: 'Beginner',
    students: 2587,
    price: 0,
    featured: true,
  },
];

const Courses = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('');
  const [selectedLevel, setSelectedLevel] = useState('');
  const [selectedDuration, setSelectedDuration] = useState('');
  const [selectedPrice, setSelectedPrice] = useState('');
  
  const categories = ['Computer Science', 'Business', 'Web Development', 'Data Science', 'Marketing', 'Economics', 'Design', 'Psychology'];
  const levels = ['Beginner', 'Intermediate', 'Advanced'];
  const durations = ['< 10 weeks', '10-12 weeks', '> 12 weeks'];
  const prices = ['Free', 'Under $50', '$50-$70', '$70-$100'];
  
  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(e.target.value);
  };
  
  const filteredCourses = allCourses.filter(course => {
    const matchesSearch = course.title.toLowerCase().includes(searchTerm.toLowerCase()) || 
                          course.instructor.toLowerCase().includes(searchTerm.toLowerCase()) ||
                          course.category.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesCategory = selectedCategory === '' || course.category === selectedCategory;
    
    const matchesLevel = selectedLevel === '' || course.level === selectedLevel;
    
    let matchesDuration = true;
    if (selectedDuration === '< 10 weeks') {
      matchesDuration = parseInt(course.duration) < 10;
    } else if (selectedDuration === '10-12 weeks') {
      const weeks = parseInt(course.duration);
      matchesDuration = weeks >= 10 && weeks <= 12;
    } else if (selectedDuration === '> 12 weeks') {
      matchesDuration = parseInt(course.duration) > 12;
    }
    
    let matchesPrice = true;
    if (selectedPrice === 'Free') {
      matchesPrice = course.price === 0;
    } else if (selectedPrice === 'Under $50') {
      matchesPrice = course.price > 0 && course.price < 50;
    } else if (selectedPrice === '$50-$70') {
      matchesPrice = course.price >= 50 && course.price <= 70;
    } else if (selectedPrice === '$70-$100') {
      matchesPrice = course.price > 70 && course.price <= 100;
    }
    
    return matchesSearch && matchesCategory && matchesLevel && matchesDuration && matchesPrice;
  });
  
  const clearFilters = () => {
    setSearchTerm('');
    setSelectedCategory('');
    setSelectedLevel('');
    setSelectedDuration('');
    setSelectedPrice('');
  };
  
  return (
    <div className="page-transition pt-24">
      {/* Header */}
      <div className="bg-gradient-to-r from-primary/10 to-primary/5 py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="max-w-3xl">
            <span className="inline-block text-sm font-medium text-primary mb-2">Course Catalog</span>
            <h1 className="text-4xl md:text-5xl font-bold mb-6">Discover Our Courses</h1>
            <p className="text-lg text-muted-foreground mb-8">
              Browse our comprehensive selection of courses designed to help you achieve your educational and career goals.
            </p>
          </div>
        </div>
      </div>
      
      {/* Search and Filter Section */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="flex flex-col lg:flex-row gap-6">
          {/* Search */}
          <div className="flex-1">
            <div className="relative">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <Search className="h-5 w-5 text-muted-foreground" />
              </div>
              <input
                type="text"
                className="block w-full pl-10 pr-4 py-3 border border-input rounded-md bg-background focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent transition-all"
                placeholder="Search courses by title, instructor or category..."
                value={searchTerm}
                onChange={handleSearch}
              />
            </div>
          </div>
          
          {/* Filters */}
          <div className="flex flex-wrap gap-4">
            {/* Category Filter */}
            <div className="relative">
              <select
                className="appearance-none block w-full px-4 py-3 pr-8 border border-input rounded-md bg-background focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent transition-all"
                value={selectedCategory}
                onChange={(e) => setSelectedCategory(e.target.value)}
              >
                <option value="">All Categories</option>
                {categories.map((category) => (
                  <option key={category} value={category}>
                    {category}
                  </option>
                ))}
              </select>
              <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                <ChevronDown className="h-4 w-4 text-muted-foreground" />
              </div>
            </div>
            
            {/* Level Filter */}
            <div className="relative">
              <select
                className="appearance-none block w-full px-4 py-3 pr-8 border border-input rounded-md bg-background focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent transition-all"
                value={selectedLevel}
                onChange={(e) => setSelectedLevel(e.target.value)}
              >
                <option value="">All Levels</option>
                {levels.map((level) => (
                  <option key={level} value={level}>
                    {level}
                  </option>
                ))}
              </select>
              <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                <ChevronDown className="h-4 w-4 text-muted-foreground" />
              </div>
            </div>
            
            {/* Duration Filter */}
            <div className="relative">
              <select
                className="appearance-none block w-full px-4 py-3 pr-8 border border-input rounded-md bg-background focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent transition-all"
                value={selectedDuration}
                onChange={(e) => setSelectedDuration(e.target.value)}
              >
                <option value="">All Durations</option>
                {durations.map((duration) => (
                  <option key={duration} value={duration}>
                    {duration}
                  </option>
                ))}
              </select>
              <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                <ChevronDown className="h-4 w-4 text-muted-foreground" />
              </div>
            </div>
            
            {/* Price Filter */}
            <div className="relative">
              <select
                className="appearance-none block w-full px-4 py-3 pr-8 border border-input rounded-md bg-background focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent transition-all"
                value={selectedPrice}
                onChange={(e) => setSelectedPrice(e.target.value)}
              >
                <option value="">All Prices</option>
                {prices.map((price) => (
                  <option key={price} value={price}>
                    {price}
                  </option>
                ))}
              </select>
              <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                <ChevronDown className="h-4 w-4 text-muted-foreground" />
              </div>
            </div>
            
            {/* Clear Filters Button */}
            <button
              onClick={clearFilters}
              className="px-4 py-3 border border-input rounded-md bg-background hover:bg-secondary transition-colors"
            >
              Clear Filters
            </button>
          </div>
        </div>
        
        {/* Results Count */}
        <div className="mt-6 mb-8 flex items-center justify-between">
          <p className="text-muted-foreground">
            Showing <span className="font-medium text-foreground">{filteredCourses.length}</span> {filteredCourses.length === 1 ? 'course' : 'courses'}
          </p>
          <div className="flex items-center">
            <Filter className="h-5 w-5 text-muted-foreground mr-2" />
            <span className="text-sm text-muted-foreground">Filters Applied</span>
          </div>
        </div>
        
        {/* Courses Grid */}
        {filteredCourses.length > 0 ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {filteredCourses.map((course) => (
              <CourseCard key={course.id} {...course} />
            ))}
          </div>
        ) : (
          <div className="text-center py-20">
            <h3 className="text-xl font-semibold mb-2">No courses found</h3>
            <p className="text-muted-foreground mb-4">Try adjusting your search or filter criteria</p>
            <button
              onClick={clearFilters}
              className="px-4 py-2 border border-input rounded-md bg-primary text-white hover:bg-primary/90 transition-colors"
            >
              Clear All Filters
            </button>
          </div>
        )}
      </div>
    </div>
  );
};

export default Courses;
