
import React from 'react';
import { Link } from 'react-router-dom';
import { Clock, Users, Award } from 'lucide-react';
import { cn } from '@/lib/utils';

export interface CourseCardProps {
  id: number;
  title: string;
  category: string;
  image: string;
  instructor: string;
  duration: string;
  level: string;
  students: number;
  price: number;
  featured?: boolean;
}

const CourseCard: React.FC<CourseCardProps> = ({
  id,
  title,
  category,
  image,
  instructor,
  duration,
  level,
  students,
  price,
  featured = false,
}) => {
  return (
    <div 
      className={cn(
        "group rounded-xl overflow-hidden bg-card shadow-sm border border-border card-hover",
        featured && "ring-2 ring-primary"
      )}
    >
      <div className="relative">
        <Link to={`/courses/${id}`}>
          <img 
            src={image} 
            alt={title}
            className="w-full h-48 object-cover transition-transform duration-500 group-hover:scale-105"
          />
        </Link>
        <div className="absolute top-3 left-3">
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-background text-foreground">
            {category}
          </span>
        </div>
        {featured && (
          <div className="absolute top-3 right-3">
            <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-primary text-primary-foreground">
              Featured
            </span>
          </div>
        )}
      </div>
      
      <div className="p-5">
        <Link to={`/courses/${id}`}>
          <h3 className="text-lg font-semibold mb-2 transition-colors group-hover:text-primary">{title}</h3>
        </Link>
        
        <p className="text-sm text-muted-foreground mb-4">By {instructor}</p>
        
        <div className="flex flex-wrap gap-3 mb-4">
          <div className="flex items-center text-xs text-muted-foreground">
            <Clock className="h-3.5 w-3.5 mr-1 text-primary" />
            {duration}
          </div>
          
          <div className="flex items-center text-xs text-muted-foreground">
            <Award className="h-3.5 w-3.5 mr-1 text-primary" />
            {level}
          </div>
          
          <div className="flex items-center text-xs text-muted-foreground">
            <Users className="h-3.5 w-3.5 mr-1 text-primary" />
            {students} students
          </div>
        </div>
        
        <div className="flex items-center justify-between pt-4 border-t border-border">
          <div className="text-lg font-bold text-foreground">
            {price === 0 ? (
              <span className="text-green-500">Free</span>
            ) : (
              <span>${price}</span>
            )}
          </div>
          
          <Link 
            to={`/courses/${id}`}
            className="inline-flex items-center justify-center px-3 py-1.5 text-sm font-medium text-primary hover:text-primary-foreground hover:bg-primary rounded-md transition-colors"
          >
            View Course
          </Link>
        </div>
      </div>
    </div>
  );
};

export default CourseCard;
